<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量操作功能预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Inter', sans-serif;
            background-color: #F9FAFB;
            padding: 24px;
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 24px;
        }

        .filter-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
        }

        .filter-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            width: 128px;
        }

        .batch-operation {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: #FAFAFA;
            border: 1px solid #E5E7EB;
        }

        .batch-operation.has-selection {
            background: #F0F9FF;
            border-color: #3B82F6;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }

        .batch-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .batch-selected {
            font-size: 14px;
            color: #6B7280;
            white-space: nowrap;
            min-width: 80px;
            font-weight: 500;
        }

        .batch-actions {
            display: flex;
            align-items: center;
            gap: 4px;
            animation: fadeIn 0.3s ease;
        }

        .batch-action-link {
            font-size: 12px;
            color: #3B82F6;
            padding: 0 4px;
            background: none;
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

        .batch-action-link:hover {
            color: #1D4ED8;
            text-decoration: underline;
        }

        .batch-divider {
            color: #D1D5DB;
            font-size: 12px;
            margin: 0 2px;
        }

        .batch-delete-button {
            border: 1px solid #D1D5DB;
            background: #FFFFFF;
            color: #374151;
            font-size: 14px;
            height: 32px;
            padding: 0 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .batch-delete-button:hover:not(:disabled) {
            border-color: #EF4444;
            background: #FEF2F2;
            color: #EF4444;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
        }

        .batch-delete-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #D1D5DB;
            background: #F9FAFB;
            color: #9CA3AF;
        }

        .new-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .new-button:hover {
            background: #2563EB;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #E5E7EB;
        }

        .demo-table th {
            background: #F9FAFB;
            font-weight: 600;
            color: #374151;
        }

        .demo-table tr:hover {
            background: #F9FAFB;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #DCFCE7;
            color: #16A34A;
        }

        .status-error {
            background: #FEE2E2;
            color: #DC2626;
        }

        .status-pending {
            background: #FEF3C7;
            color: #D97706;
        }

        .controls {
            margin-bottom: 16px;
            display: flex;
            gap: 12px;
        }

        .control-button {
            padding: 6px 12px;
            border: 1px solid #D1D5DB;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .control-button:hover {
            background: #F3F4F6;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">批量操作功能预览</h1>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button class="control-button" onclick="selectItems(0)">选择0项</button>
            <button class="control-button" onclick="selectItems(1)">选择1项</button>
            <button class="control-button" onclick="selectItems(3)">选择3项</button>
            <button class="control-button" onclick="selectItems(5)">选择全部</button>
        </div>

        <!-- 筛选和操作区域 -->
        <div class="filter-section">
            <div class="filter-left">
                <input type="text" class="search-input" placeholder="输入文件名称">
                <select class="filter-select">
                    <option>审查结果</option>
                    <option>未发现风险</option>
                    <option>发现风险</option>
                </select>
            </div>

            <div class="filter-right">
                <!-- 批量操作 -->
                <div class="batch-operation" id="batchOperation">
                    <div class="batch-info">
                        <span class="batch-selected" id="batchSelected">已选择 0 项</span>
                        <div class="batch-actions" id="batchActions" style="display: none;">
                            <button class="batch-action-link" onclick="selectAll()">全选</button>
                            <span class="batch-divider">|</span>
                            <button class="batch-action-link" onclick="clearSelection()">取消全选</button>
                        </div>
                    </div>
                    <button class="batch-delete-button" id="batchDeleteBtn" disabled onclick="batchDelete()">
                        批量删除
                    </button>
                </div>

                <!-- 新建审查任务 -->
                <button class="new-button">+ 新建审查</button>
            </div>
        </div>

        <!-- 示例表格 -->
        <table class="demo-table">
            <thead>
                <tr>
                    <th><input type="checkbox" class="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()"></th>
                    <th>序号</th>
                    <th>采购项目名称</th>
                    <th>文件名称</th>
                    <th>审查结果</th>
                    <th>创建人</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><input type="checkbox" class="checkbox row-checkbox" onchange="updateSelection()"></td>
                    <td>1</td>
                    <td>深圳大学多光子成像显微镜采购</td>
                    <td>SZCG2025000310-A.docx</td>
                    <td><span class="status-badge status-success">未发现风险</span></td>
                    <td>张三</td>
                    <td>2024-01-15 10:30</td>
                    <td>查看 | 导出 | 删除</td>
                </tr>
                <tr>
                    <td><input type="checkbox" class="checkbox row-checkbox" onchange="updateSelection()"></td>
                    <td>2</td>
                    <td>办公设备采购项目</td>
                    <td>办公设备采购文件.docx</td>
                    <td><span class="status-badge status-error">发现风险</span></td>
                    <td>李四</td>
                    <td>2024-01-15 09:15</td>
                    <td>查看 | 导出 | 删除</td>
                </tr>
                <tr>
                    <td><input type="checkbox" class="checkbox row-checkbox" onchange="updateSelection()"></td>
                    <td>3</td>
                    <td>实验室设备采购</td>
                    <td>实验设备采购方案.docx</td>
                    <td><span class="status-badge status-pending">审查中</span></td>
                    <td>王五</td>
                    <td>2024-01-15 08:45</td>
                    <td>查看 | 导出 | 删除</td>
                </tr>
                <tr>
                    <td><input type="checkbox" class="checkbox row-checkbox" onchange="updateSelection()"></td>
                    <td>4</td>
                    <td>图书馆设备采购</td>
                    <td>图书馆设备清单.docx</td>
                    <td><span class="status-badge status-success">未发现风险</span></td>
                    <td>赵六</td>
                    <td>2024-01-14 16:20</td>
                    <td>查看 | 导出 | 删除</td>
                </tr>
                <tr>
                    <td><input type="checkbox" class="checkbox row-checkbox" onchange="updateSelection()"></td>
                    <td>5</td>
                    <td>教学设备采购项目</td>
                    <td>教学设备采购文件.docx</td>
                    <td><span class="status-badge status-error">发现风险</span></td>
                    <td>孙七</td>
                    <td>2024-01-14 14:10</td>
                    <td>查看 | 导出 | 删除</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        let selectedCount = 0;
        const totalRows = 5;

        function updateBatchOperation() {
            const batchOperation = document.getElementById('batchOperation');
            const batchSelected = document.getElementById('batchSelected');
            const batchActions = document.getElementById('batchActions');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');

            batchSelected.textContent = `已选择 ${selectedCount} 项`;
            
            if (selectedCount > 0) {
                batchOperation.classList.add('has-selection');
                batchActions.style.display = 'flex';
                batchDeleteBtn.disabled = false;
            } else {
                batchOperation.classList.remove('has-selection');
                batchActions.style.display = 'none';
                batchDeleteBtn.disabled = true;
            }
        }

        function updateSelection() {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            updateBatchOperation();
            
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            selectAllCheckbox.checked = selectedCount === totalRows;
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalRows;
        }

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAllCheckbox.checked;
            });
            
            selectedCount = selectAllCheckbox.checked ? totalRows : 0;
            updateBatchOperation();
        }

        function selectAll() {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            checkboxes.forEach(cb => {
                cb.checked = true;
            });
            selectAllCheckbox.checked = true;
            selectedCount = totalRows;
            updateBatchOperation();
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            checkboxes.forEach(cb => {
                cb.checked = false;
            });
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
            selectedCount = 0;
            updateBatchOperation();
        }

        function selectItems(count) {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            checkboxes.forEach((cb, index) => {
                cb.checked = index < count;
            });
            
            selectedCount = count;
            selectAllCheckbox.checked = count === totalRows;
            selectAllCheckbox.indeterminate = count > 0 && count < totalRows;
            updateBatchOperation();
        }

        function batchDelete() {
            if (selectedCount > 0) {
                alert(`确定要删除选中的 ${selectedCount} 个任务吗？删除后将无法找回！`);
            }
        }

        // 初始化
        updateBatchOperation();
    </script>
</body>
</html>
