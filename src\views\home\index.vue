<template>
  <div class="home">
    <div class="title"><i class="icon"></i>您好，欢迎使用采购文件合规性审查</div>
    <div class="tip">
      <div  style="margin-top: -10px;">
        请先上传采购文件，
      </div>
      <div class="text-box">
        <span class="text">解锁审查新旅程</span>
      </div>
    </div> 
    <uploader></uploader>
  </div>
</template>

<script setup lang='ts'> 
import Uploader from '@/views/home/<USER>/uploader.vue'  
</script>

<style lang="scss" scoped>
$color-add-text: #4E5969;
.home {
  max-width: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 16px; 
  .title {
    display: flex;
    align-items: center;
    font-size: 34px;
    font-weight: 500;
    margin-top: 32px;
    .icon {
      display: inline-block;
      vertical-align: middle;
      margin: -3px 8px 0;
      width: 48px;
      height: 48px;
      background: url('@/assets/images/bid-examine/home-welcome.png') no-repeat;
      background-size: 100% auto;
    }
  }
  .tip {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 500;
    margin: 4px 0 36px;
    .text-box {
      background: url('@/assets/images/bid-examine/home-text-bg.png') no-repeat center bottom;
      background-size: 100% auto;
      padding-bottom: 12px;
      .text {
        font-size: 24px;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        background: linear-gradient(96deg, #165DFF 1%, #8142FF 99%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  } 
}
</style>
